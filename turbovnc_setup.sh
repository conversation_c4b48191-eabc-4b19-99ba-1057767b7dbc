#!/bin/bash
# TurboVNC + Open3D 配置脚本
# 专门为SSH连接的远程服务器设计

set -e

echo "🚀 TurboVNC + Open3D 配置脚本"
echo "适用于SSH连接的远程服务器"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取服务器信息
SERVER_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")
HOSTNAME=$(hostname)

echo -e "${BLUE}📡 服务器信息:${NC}"
echo "   主机名: $HOSTNAME"
echo "   内网IP: $SERVER_IP"
echo "   SSH端口: 1020 (根据你的连接信息)"

# 检查系统类型
detect_system() {
    if [ -f /etc/debian_version ]; then
        echo "ubuntu"
    elif [ -f /etc/redhat-release ]; then
        echo "centos"
    else
        echo "unknown"
    fi
}

SYSTEM=$(detect_system)
echo -e "${BLUE}🖥️  系统类型: $SYSTEM${NC}"

# 安装TurboVNC
install_turbovnc() {
    echo -e "${BLUE}📦 安装TurboVNC...${NC}"
    
    case $SYSTEM in
        "ubuntu")
            # Ubuntu/Debian
            echo "为Ubuntu/Debian安装TurboVNC..."
            
            # 更新包列表
            apt update
            
            # 安装依赖
            apt install -y wget curl xfce4 xfce4-goodies firefox
            
            # 下载并安装TurboVNC
            cd /tmp
            wget https://sourceforge.net/projects/turbovnc/files/3.0.3/turbovnc_3.0.3_amd64.deb/download -O turbovnc.deb
            dpkg -i turbovnc.deb || apt-get install -f -y
            
            # 安装VirtualGL (可选，用于3D加速)
            wget https://sourceforge.net/projects/virtualgl/files/3.1/virtualgl_3.1_amd64.deb/download -O virtualgl.deb
            dpkg -i virtualgl.deb || apt-get install -f -y
            ;;
            
        "centos")
            # CentOS/RHEL
            echo "为CentOS/RHEL安装TurboVNC..."
            
            # 安装EPEL
            yum install -y epel-release
            
            # 安装依赖
            yum install -y wget curl
            yum groupinstall -y "Xfce"
            
            # 下载并安装TurboVNC
            cd /tmp
            wget https://sourceforge.net/projects/turbovnc/files/3.0.3/turbovnc-3.0.3.x86_64.rpm/download -O turbovnc.rpm
            rpm -ivh turbovnc.rpm
            
            # 安装VirtualGL
            wget https://sourceforge.net/projects/virtualgl/files/3.1/VirtualGL-3.1.x86_64.rpm/download -O virtualgl.rpm
            rpm -ivh virtualgl.rpm
            ;;
            
        *)
            echo -e "${RED}❌ 不支持的系统类型${NC}"
            exit 1
            ;;
    esac
    
    echo -e "${GREEN}✅ TurboVNC安装完成${NC}"
}

# 配置TurboVNC
configure_turbovnc() {
    echo -e "${BLUE}⚙️  配置TurboVNC...${NC}"
    
    # 创建VNC目录
    mkdir -p ~/.vnc
    
    # 创建xstartup文件
    cat > ~/.vnc/xstartup << 'EOF'
#!/bin/bash
# TurboVNC启动脚本

# 加载X资源
[ -r $HOME/.Xresources ] && xrdb $HOME/.Xresources

# 设置背景色
xsetroot -solid grey

# 启动XFCE桌面环境
startxfce4 &

# 启动终端 (可选)
# xfce4-terminal &
EOF
    
    # 给执行权限
    chmod +x ~/.vnc/xstartup
    
    # 创建TurboVNC配置文件
    cat > ~/.vnc/turbovncserver.conf << 'EOF'
# TurboVNC服务器配置

# 默认几何尺寸
$geometry = "1920x1080";

# 颜色深度
$depth = 24;

# 桌面环境
$wm = "startxfce4";

# 字体路径
$fontPath = "";

# 安全类型
$securityTypes = "VNC";
EOF
    
    echo -e "${GREEN}✅ TurboVNC配置完成${NC}"
}

# 安装Python和Open3D
install_python_open3d() {
    echo -e "${BLUE}🐍 安装Python和Open3D...${NC}"
    
    case $SYSTEM in
        "ubuntu")
            apt install -y python3 python3-pip python3-dev
            ;;
        "centos")
            yum install -y python3 python3-pip python3-devel
            ;;
    esac
    
    # 升级pip
    python3 -m pip install --upgrade pip
    
    # 安装Open3D和相关包
    pip3 install open3d numpy matplotlib plotly
    
    echo -e "${GREEN}✅ Python和Open3D安装完成${NC}"
}

# 启动TurboVNC服务器
start_turbovnc() {
    echo -e "${BLUE}🖥️  启动TurboVNC服务器...${NC}"
    
    # 检查是否已有VNC会话
    if pgrep -f "Xvnc.*:1" > /dev/null; then
        echo -e "${YELLOW}⚠️  VNC会话:1已在运行${NC}"
        /opt/TurboVNC/bin/vncserver -kill :1 2>/dev/null || true
        sleep 2
    fi
    
    # 设置VNC密码 (如果没有)
    if [ ! -f ~/.vnc/passwd ]; then
        echo -e "${YELLOW}🔐 设置VNC密码:${NC}"
        /opt/TurboVNC/bin/vncpasswd
    fi
    
    # 启动TurboVNC服务器
    echo "启动TurboVNC服务器..."
    /opt/TurboVNC/bin/vncserver :1 -geometry 1920x1080 -depth 24
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ TurboVNC服务器启动成功${NC}"
    else
        echo -e "${RED}❌ TurboVNC服务器启动失败${NC}"
        exit 1
    fi
}

# 显示连接信息
show_connection_info() {
    echo ""
    echo -e "${GREEN}🎉 TurboVNC环境配置完成!${NC}"
    echo "=================================="
    echo -e "${BLUE}📡 连接方式:${NC}"
    echo ""
    echo -e "${YELLOW}方法1: SSH隧道 + VNC客户端 (推荐)${NC}"
    echo "1. 在本地建立SSH隧道:"
    echo "   ssh -L 5901:localhost:5901 root@************ -p 1020"
    echo ""
    echo "2. 保持SSH连接，然后用VNC客户端连接:"
    echo "   地址: localhost:5901"
    echo "   或者: 127.0.0.1:5901"
    echo ""
    echo -e "${YELLOW}方法2: 直接VNC连接 (需要防火墙开放端口)${NC}"
    echo "   地址: ************:5901"
    echo "   (需要服务器开放5901端口)"
    echo ""
    echo -e "${BLUE}🖥️  推荐VNC客户端:${NC}"
    echo "   • TurboVNC Viewer (最佳兼容性)"
    echo "     下载: https://turbovnc.org/"
    echo "   • TightVNC Viewer"
    echo "   • RealVNC Viewer"
    echo ""
    echo -e "${BLUE}🧪 测试Open3D:${NC}"
    echo "   连接VNC后，在桌面打开终端运行:"
    echo "   cd $(pwd)"
    echo "   python3 vnc_open3d_test.py"
    echo "   python3 open3d_minimal_demo.py"
    echo ""
    echo -e "${BLUE}🔧 TurboVNC管理命令:${NC}"
    echo "   启动: /opt/TurboVNC/bin/vncserver :1"
    echo "   停止: /opt/TurboVNC/bin/vncserver -kill :1"
    echo "   查看: /opt/TurboVNC/bin/vncserver -list"
}

# 创建快速启动脚本
create_quick_start() {
    cat > ~/start_turbovnc.sh << 'EOF'
#!/bin/bash
# TurboVNC快速启动脚本

echo "🚀 启动TurboVNC..."

# 停止现有会话
/opt/TurboVNC/bin/vncserver -kill :1 2>/dev/null || true

# 启动新会话
/opt/TurboVNC/bin/vncserver :1 -geometry 1920x1080 -depth 24

echo "✅ TurboVNC已启动"
echo "📡 连接地址: localhost:5901 (通过SSH隧道)"
echo "🔗 SSH隧道命令: ssh -L 5901:localhost:5901 root@************ -p 1020"
EOF
    
    chmod +x ~/start_turbovnc.sh
    echo -e "${GREEN}✅ 快速启动脚本已创建: ~/start_turbovnc.sh${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}开始配置TurboVNC环境...${NC}"
    
    # 检查权限
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}❌ 此脚本需要root权限运行${NC}"
        echo "请使用: sudo $0"
        exit 1
    fi
    
    # 安装TurboVNC
    if ! command -v /opt/TurboVNC/bin/vncserver &> /dev/null; then
        install_turbovnc
    else
        echo -e "${GREEN}✅ TurboVNC已安装${NC}"
    fi
    
    # 配置TurboVNC
    configure_turbovnc
    
    # 安装Python和Open3D
    install_python_open3d
    
    # 启动TurboVNC
    start_turbovnc
    
    # 创建快速启动脚本
    create_quick_start
    
    # 显示连接信息
    show_connection_info
}

# 运行主函数
main "$@"
