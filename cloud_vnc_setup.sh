#!/bin/bash
# 云服务器TurboVNC一键配置脚本
# 专为 ssh -X root@************ -p 1020 这种连接方式设计

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🌐 云服务器TurboVNC + Open3D 一键配置${NC}"
echo "=================================================="
echo -e "${BLUE}适用于SSH连接: ssh -X root@************ -p 1020${NC}"
echo "=================================================="

# 检查系统
check_system() {
    echo -e "${BLUE}🔍 检查系统环境...${NC}"
    
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}❌ 请使用root权限运行此脚本${NC}"
        exit 1
    fi
    
    if [ -f /etc/debian_version ]; then
        echo -e "${GREEN}✅ 检测到Ubuntu/Debian系统${NC}"
        SYSTEM="debian"
    elif [ -f /etc/redhat-release ]; then
        echo -e "${GREEN}✅ 检测到CentOS/RHEL系统${NC}"
        SYSTEM="redhat"
    else
        echo -e "${RED}❌ 不支持的系统类型${NC}"
        exit 1
    fi
}

# 安装基础环境
install_base() {
    echo -e "${BLUE}📦 安装基础环境...${NC}"
    
    case $SYSTEM in
        "debian")
            apt update
            apt install -y wget curl xfce4 xfce4-goodies firefox-esr
            apt install -y python3 python3-pip python3-dev
            ;;
        "redhat")
            yum install -y epel-release
            yum install -y wget curl
            yum groupinstall -y "Xfce"
            yum install -y python3 python3-pip python3-devel
            ;;
    esac
    
    echo -e "${GREEN}✅ 基础环境安装完成${NC}"
}

# 安装TurboVNC
install_turbovnc() {
    echo -e "${BLUE}📦 安装TurboVNC...${NC}"
    
    cd /tmp
    
    case $SYSTEM in
        "debian")
            wget -O turbovnc.deb "https://sourceforge.net/projects/turbovnc/files/3.0.3/turbovnc_3.0.3_amd64.deb/download"
            dpkg -i turbovnc.deb || apt-get install -f -y
            ;;
        "redhat")
            wget -O turbovnc.rpm "https://sourceforge.net/projects/turbovnc/files/3.0.3/turbovnc-3.0.3.x86_64.rpm/download"
            rpm -ivh turbovnc.rpm
            ;;
    esac
    
    echo -e "${GREEN}✅ TurboVNC安装完成${NC}"
}

# 安装Open3D
install_open3d() {
    echo -e "${BLUE}🐍 安装Python环境和Open3D...${NC}"
    
    # 升级pip
    python3 -m pip install --upgrade pip
    
    # 安装Open3D和相关包
    pip3 install open3d numpy matplotlib plotly pandas
    
    echo -e "${GREEN}✅ Open3D环境安装完成${NC}"
}

# 配置TurboVNC
configure_vnc() {
    echo -e "${BLUE}⚙️  配置TurboVNC...${NC}"
    
    # 创建VNC目录
    mkdir -p ~/.vnc
    
    # 创建启动脚本
    cat > ~/.vnc/xstartup << 'EOF'
#!/bin/bash
# TurboVNC启动脚本

# 加载X资源
[ -r $HOME/.Xresources ] && xrdb $HOME/.Xresources

# 设置背景
xsetroot -solid grey

# 启动XFCE桌面
startxfce4 &
EOF
    
    chmod +x ~/.vnc/xstartup
    
    echo -e "${GREEN}✅ TurboVNC配置完成${NC}"
}

# 设置VNC密码
setup_password() {
    echo -e "${BLUE}🔐 设置VNC密码...${NC}"
    echo -e "${YELLOW}请设置VNC连接密码 (6-8位):${NC}"
    
    /opt/TurboVNC/bin/vncpasswd
    
    echo -e "${GREEN}✅ VNC密码设置完成${NC}"
}

# 启动VNC服务器
start_vnc() {
    echo -e "${BLUE}🚀 启动TurboVNC服务器...${NC}"
    
    # 停止现有会话
    /opt/TurboVNC/bin/vncserver -kill :1 2>/dev/null || true
    sleep 2
    
    # 启动新会话 (只监听本地，安全)
    /opt/TurboVNC/bin/vncserver :1 \
        -geometry 1920x1080 \
        -depth 24 \
        -localhost \
        -noxstartup
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ TurboVNC服务器启动成功${NC}"
    else
        echo -e "${RED}❌ TurboVNC服务器启动失败${NC}"
        exit 1
    fi
}

# 创建测试脚本
create_test_script() {
    echo -e "${BLUE}📝 创建测试脚本...${NC}"
    
    cat > ~/test_open3d_vnc.py << 'EOF'
#!/usr/bin/env python3
"""云服务器VNC环境Open3D测试"""

import numpy as np
import os

def test_open3d():
    print("🧪 测试Open3D...")
    
    try:
        import open3d as o3d
        print(f"✅ Open3D版本: {o3d.__version__}")
        
        # 创建测试场景
        sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.5)
        sphere.paint_uniform_color([1, 0, 0])
        
        box = o3d.geometry.TriangleMesh.create_box()
        box.translate([2, 0, 0])
        box.paint_uniform_color([0, 1, 0])
        
        coord = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
        
        print("🖼️  启动可视化窗口...")
        print("💡 操作: 鼠标拖拽旋转，滚轮缩放，Q键退出")
        
        o3d.visualization.draw_geometries(
            [sphere, box, coord],
            window_name="云服务器VNC + Open3D测试",
            width=1200,
            height=800
        )
        
        print("🎉 测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🌐 云服务器VNC环境Open3D测试")
    print("DISPLAY:", os.environ.get('DISPLAY', 'Not set'))
    test_open3d()
EOF
    
    chmod +x ~/test_open3d_vnc.py
    
    echo -e "${GREEN}✅ 测试脚本创建完成: ~/test_open3d_vnc.py${NC}"
}

# 创建管理脚本
create_management_scripts() {
    echo -e "${BLUE}📝 创建管理脚本...${NC}"
    
    # VNC启动脚本
    cat > ~/start_vnc.sh << 'EOF'
#!/bin/bash
echo "🚀 启动TurboVNC..."
/opt/TurboVNC/bin/vncserver -kill :1 2>/dev/null || true
/opt/TurboVNC/bin/vncserver :1 -geometry 1920x1080 -depth 24 -localhost
echo "✅ VNC已启动"
echo "📡 SSH隧道: ssh -L 5901:localhost:5901 root@************ -p 1020"
EOF
    
    # VNC停止脚本
    cat > ~/stop_vnc.sh << 'EOF'
#!/bin/bash
echo "🛑 停止TurboVNC..."
/opt/TurboVNC/bin/vncserver -kill :1
echo "✅ VNC已停止"
EOF
    
    # VNC状态脚本
    cat > ~/vnc_status.sh << 'EOF'
#!/bin/bash
echo "📊 TurboVNC状态:"
/opt/TurboVNC/bin/vncserver -list
echo ""
echo "🔍 进程状态:"
ps aux | grep Xvnc | grep -v grep
EOF
    
    chmod +x ~/start_vnc.sh ~/stop_vnc.sh ~/vnc_status.sh
    
    echo -e "${GREEN}✅ 管理脚本创建完成${NC}"
}

# 显示使用说明
show_usage() {
    echo ""
    echo -e "${GREEN}🎉 云服务器TurboVNC配置完成!${NC}"
    echo "=================================================="
    echo ""
    echo -e "${CYAN}📡 连接方式 (SSH隧道 - 推荐):${NC}"
    echo -e "${YELLOW}1. 在本地电脑新开终端，建立SSH隧道:${NC}"
    echo "   ssh -L 5901:localhost:5901 root@************ -p 1020"
    echo ""
    echo -e "${YELLOW}2. 保持SSH连接，下载并打开VNC客户端:${NC}"
    echo "   • TurboVNC Viewer: https://turbovnc.org/"
    echo "   • 连接地址: localhost:5901"
    echo "   • 输入刚才设置的VNC密码"
    echo ""
    echo -e "${CYAN}🧪 测试Open3D:${NC}"
    echo "   在VNC桌面中打开终端，运行:"
    echo "   python3 ~/test_open3d_vnc.py"
    echo ""
    echo -e "${CYAN}🔧 VNC管理命令:${NC}"
    echo "   启动VNC: ~/start_vnc.sh"
    echo "   停止VNC: ~/stop_vnc.sh"
    echo "   查看状态: ~/vnc_status.sh"
    echo ""
    echo -e "${CYAN}📁 相关文件:${NC}"
    echo "   测试脚本: ~/test_open3d_vnc.py"
    echo "   VNC日志: ~/.vnc/*.log"
    echo ""
    echo -e "${GREEN}✨ 现在可以通过VNC使用Open3D了!${NC}"
}

# 主函数
main() {
    check_system
    install_base
    install_turbovnc
    install_open3d
    configure_vnc
    setup_password
    start_vnc
    create_test_script
    create_management_scripts
    show_usage
}

# 错误处理
trap 'echo -e "\n${RED}❌ 安装过程中出现错误${NC}"; exit 1' ERR

# 运行主函数
main "$@"
