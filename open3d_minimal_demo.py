#!/usr/bin/env python3
"""
Open3D 最小demo - 点云和3D边界框可视化
展示Open3D的基本功能：创建点云、添加边界框、交互式可视化
"""

import numpy as np
import open3d as o3d

def create_sample_pointcloud(n_points=1000):
    """创建示例点云数据"""
    # 生成随机点云 - 模拟激光雷达数据
    points = np.random.randn(n_points, 3) * 5  # 随机分布在5米范围内
    
    # 添加一些结构化的点（模拟车辆、建筑物等）
    # 车辆1 - 长方体
    car1_points = np.random.uniform([-1, -0.5, 0], [3, 0.5, 1.5], (200, 3))
    car1_points[:, 0] += 10  # 移动到x=10位置
    
    # 车辆2 - 另一个长方体
    car2_points = np.random.uniform([-1, -0.5, 0], [3, 0.5, 1.5], (200, 3))
    car2_points[:, 1] += 8   # 移动到y=8位置
    
    # 地面点
    ground_points = np.random.uniform([-20, -20, -0.2], [20, 20, 0.2], (500, 3))
    
    # 合并所有点
    all_points = np.vstack([points, car1_points, car2_points, ground_points])
    
    # 生成强度值（模拟激光反射强度）
    intensities = np.random.randint(0, 255, len(all_points))
    
    return all_points, intensities

def intensity_to_color(intensities, colormap='jet'):
    """将强度值转换为颜色"""
    # 归一化强度值到[0,1]
    norm_intensities = (intensities - intensities.min()) / (intensities.max() - intensities.min())
    
    if colormap == 'jet':
        # 使用jet colormap
        colors = np.zeros((len(intensities), 3))
        colors[:, 0] = np.clip(1.5 - 4 * np.abs(norm_intensities - 0.75), 0, 1)  # Red
        colors[:, 1] = np.clip(1.5 - 4 * np.abs(norm_intensities - 0.5), 0, 1)   # Green  
        colors[:, 2] = np.clip(1.5 - 4 * np.abs(norm_intensities - 0.25), 0, 1)  # Blue
    else:
        # 默认灰度
        colors = np.column_stack([norm_intensities] * 3)
    
    return colors

def create_bounding_box(center, size, rotation=0, color=[1, 0, 0]):
    """创建3D边界框"""
    # 创建Open3D边界框
    bbox = o3d.geometry.OrientedBoundingBox()
    bbox.center = center
    bbox.extent = size
    
    # 设置旋转矩阵
    if rotation != 0:
        cos_r, sin_r = np.cos(rotation), np.sin(rotation)
        R = np.array([
            [cos_r, -sin_r, 0],
            [sin_r,  cos_r, 0],
            [0,      0,     1]
        ])
        bbox.R = R
    
    bbox.color = color
    return bbox

def create_coordinate_frame(size=2.0):
    """创建坐标系"""
    return o3d.geometry.TriangleMesh.create_coordinate_frame(size=size)

def headless_render(geometries, output_path="pointcloud_render.png"):
    """无头渲染 - 不需要图形界面"""
    print("🖼️  使用无头渲染模式...")

    try:
        # 创建无头渲染器
        vis = o3d.visualization.Visualizer()
        vis.create_window(visible=False, width=1920, height=1080)

        # 添加几何体
        for geom in geometries:
            vis.add_geometry(geom)

        # 设置视角
        ctr = vis.get_view_control()
        ctr.set_zoom(0.6)
        ctr.set_front([0.5, 0.5, 0.5])
        ctr.set_lookat([0, 0, 0])
        ctr.set_up([0, 0, 1])

        # 设置渲染选项
        render_option = vis.get_render_option()
        render_option.point_size = 2.0
        render_option.background_color = np.array([0.1, 0.1, 0.1])

        # 渲染并保存
        vis.poll_events()
        vis.update_renderer()
        vis.capture_screen_image(output_path)
        vis.destroy_window()

        print(f"✅ 渲染完成! 图片保存至: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 无头渲染失败: {e}")
        return False

def save_as_ply(geometries, output_path="pointcloud_demo.ply"):
    """保存为PLY文件"""
    print("💾 保存点云为PLY文件...")

    try:
        # 只保存点云数据
        for geom in geometries:
            if isinstance(geom, o3d.geometry.PointCloud):
                o3d.io.write_point_cloud(output_path, geom)
                print(f"✅ 点云已保存至: {output_path}")
                print("💡 可以用CloudCompare、MeshLab等工具打开查看")
                return True

        print("❌ 未找到点云数据")
        return False

    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False

def matplotlib_fallback(points, intensities, bboxes=None):
    """使用Matplotlib作为备选方案"""
    print("🎨 使用Matplotlib备选可视化...")

    try:
        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D

        # 下采样以提高性能
        if len(points) > 5000:
            indices = np.random.choice(len(points), 5000, replace=False)
            points_sample = points[indices]
            intensities_sample = intensities[indices]
        else:
            points_sample = points
            intensities_sample = intensities

        # 创建3D图
        fig = plt.figure(figsize=(15, 10))

        # 3D散点图
        ax1 = fig.add_subplot(221, projection='3d')
        scatter = ax1.scatter(
            points_sample[:, 0], points_sample[:, 1], points_sample[:, 2],
            c=intensities_sample, cmap='jet', s=0.5, alpha=0.6
        )
        ax1.set_title('3D Point Cloud')
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_zlabel('Z (m)')
        plt.colorbar(scatter, ax=ax1, shrink=0.5)

        # 俯视图
        ax2 = fig.add_subplot(222)
        scatter2 = ax2.scatter(
            points_sample[:, 0], points_sample[:, 1],
            c=intensities_sample, cmap='jet', s=0.1, alpha=0.6
        )
        ax2.set_title('Top View (XY)')
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.set_aspect('equal')
        plt.colorbar(scatter2, ax=ax2)

        # 侧视图
        ax3 = fig.add_subplot(223)
        scatter3 = ax3.scatter(
            points_sample[:, 0], points_sample[:, 2],
            c=intensities_sample, cmap='jet', s=0.1, alpha=0.6
        )
        ax3.set_title('Side View (XZ)')
        ax3.set_xlabel('X (m)')
        ax3.set_ylabel('Z (m)')
        plt.colorbar(scatter3, ax=ax3)

        # 强度分布
        ax4 = fig.add_subplot(224)
        ax4.hist(intensities_sample, bins=50, alpha=0.7, color='blue')
        ax4.set_title('Intensity Distribution')
        ax4.set_xlabel('Intensity')
        ax4.set_ylabel('Count')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('matplotlib_pointcloud.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Matplotlib可视化完成!")
        print("📁 图片已保存为: matplotlib_pointcloud.png")
        return True

    except Exception as e:
        print(f"❌ Matplotlib可视化失败: {e}")
        return False

def check_vnc_environment():
    """检查是否在VNC环境中"""
    import os

    display = os.environ.get('DISPLAY')
    if display and ':' in display:
        display_num = display.split(':')[1].split('.')[0]
        if display_num != '0':  # 不是主显示器
            return True, display_num
    return False, None

def main():
    """主函数 - Open3D最小demo (支持多种渲染模式)"""
    print("🚀 Open3D 最小demo启动...")

    # 检查环境
    is_vnc, display_num = check_vnc_environment()
    if is_vnc:
        print(f"🖥️  检测到VNC环境 (DISPLAY=:{display_num})")
        print("💡 VNC环境下Open3D应该可以正常工作!")
    else:
        print("🖥️  未检测到VNC环境，将尝试多种渲染方式")

    # 1. 创建示例点云
    print("📊 生成示例点云数据...")
    points, intensities = create_sample_pointcloud(2000)

    # 2. 创建Open3D点云对象
    print("🎨 创建Open3D点云对象...")
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)

    # 设置颜色（基于强度）
    colors = intensity_to_color(intensities)
    pcd.colors = o3d.utility.Vector3dVector(colors)

    # 3. 创建3D边界框（模拟检测到的物体）
    print("📦 创建3D边界框...")
    geometries = [pcd]

    # 车辆1的边界框
    bbox1 = create_bounding_box(
        center=[11.5, 0, 0.75],
        size=[4, 1, 1.5],
        rotation=0,
        color=[1, 0, 0]  # 红色
    )
    geometries.append(bbox1)

    # 车辆2的边界框
    bbox2 = create_bounding_box(
        center=[1.5, 8, 0.75],
        size=[4, 1, 1.5],
        rotation=np.pi/4,  # 45度旋转
        color=[0, 1, 0]    # 绿色
    )
    geometries.append(bbox2)

    # 4. 添加坐标系
    coord_frame = create_coordinate_frame(size=3.0)
    geometries.append(coord_frame)

    # 5. 尝试多种可视化方法
    print("🖼️  尝试可视化...")

    # 方法1: 尝试正常的GUI可视化
    if is_vnc:
        print("方法1: VNC环境GUI可视化...")
        print("💡 操作提示:")
        print("   - 鼠标左键拖拽: 旋转视角")
        print("   - 鼠标右键拖拽: 平移视角")
        print("   - 滚轮: 缩放")
        print("   - Q键: 退出")
    else:
        print("方法1: 尝试GUI可视化...")

    try:
        o3d.visualization.draw_geometries(
            geometries,
            window_name="Open3D 最小Demo - 点云与边界框",
            width=1200,
            height=800
        )
        print("✅ GUI可视化成功!")
        if is_vnc:
            print("🎉 VNC环境下Open3D运行完美!")
        return

    except Exception as e:
        print(f"❌ GUI可视化失败: {e}")
        if is_vnc:
            print("💡 VNC环境问题可能原因:")
            print("   1. 桌面环境未正确启动")
            print("   2. OpenGL驱动问题")
            print("   3. 尝试重启VNC服务器")

    # 方法2: 无头渲染
    print("\n方法2: 尝试无头渲染...")
    if headless_render(geometries):
        print("✅ 无头渲染成功!")

    # 方法3: 保存为PLY文件
    print("\n方法3: 保存为PLY文件...")
    save_as_ply(geometries)

    # 方法4: Matplotlib备选
    print("\n方法4: 使用Matplotlib备选...")
    matplotlib_fallback(points, intensities)

if __name__ == "__main__":
    main()
