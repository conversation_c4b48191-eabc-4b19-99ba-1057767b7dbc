#!/bin/bash
# VNC + Open3D 快速启动脚本

set -e

echo "🚀 VNC + Open3D 环境启动脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo -e "${RED}❌ 请不要以root用户运行此脚本${NC}"
    exit 1
fi

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}')
if [ -z "$SERVER_IP" ]; then
    SERVER_IP="localhost"
fi

# 检查VNC是否已安装
check_vnc_installed() {
    echo -e "${BLUE}🔍 检查VNC安装状态...${NC}"
    
    if command -v vncserver &> /dev/null; then
        echo -e "${GREEN}✅ VNC服务器已安装${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  VNC服务器未安装${NC}"
        return 1
    fi
}

# 安装VNC和桌面环境
install_vnc() {
    echo -e "${BLUE}📦 安装VNC和桌面环境...${NC}"
    
    # 检测系统类型
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        echo "检测到Debian/Ubuntu系统"
        sudo apt update
        sudo apt install -y tigervnc-standalone-server tigervnc-xorg-extension
        sudo apt install -y xfce4 xfce4-goodies
        sudo apt install -y firefox-esr  # 安装浏览器
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        echo "检测到CentOS/RHEL系统"
        sudo yum install -y epel-release
        sudo yum install -y tigervnc-server
        sudo yum groupinstall -y "Xfce"
    else
        echo -e "${RED}❌ 不支持的系统类型${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ VNC和桌面环境安装完成${NC}"
}

# 配置VNC
configure_vnc() {
    echo -e "${BLUE}⚙️  配置VNC...${NC}"
    
    # 创建VNC目录
    mkdir -p ~/.vnc
    
    # 创建xstartup文件
    cat > ~/.vnc/xstartup << 'EOF'
#!/bin/bash
xrdb $HOME/.Xresources
startxfce4 &
EOF
    
    # 给执行权限
    chmod +x ~/.vnc/xstartup
    
    echo -e "${GREEN}✅ VNC配置完成${NC}"
}

# 检查Python环境
check_python_env() {
    echo -e "${BLUE}🐍 检查Python环境...${NC}"
    
    if command -v python3 &> /dev/null; then
        echo -e "${GREEN}✅ Python3已安装${NC}"
        
        # 检查Open3D
        if python3 -c "import open3d" &> /dev/null; then
            echo -e "${GREEN}✅ Open3D已安装${NC}"
        else
            echo -e "${YELLOW}⚠️  Open3D未安装，正在安装...${NC}"
            pip3 install open3d numpy matplotlib
            echo -e "${GREEN}✅ Open3D安装完成${NC}"
        fi
    else
        echo -e "${RED}❌ Python3未安装，请先安装Python3${NC}"
        exit 1
    fi
}

# 启动VNC服务器
start_vnc() {
    echo -e "${BLUE}🖥️  启动VNC服务器...${NC}"
    
    # 检查是否已有VNC会话运行
    if pgrep -f "Xvnc.*:1" > /dev/null; then
        echo -e "${YELLOW}⚠️  VNC会话:1已在运行${NC}"
        read -p "是否要重启VNC会话? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            vncserver -kill :1 2>/dev/null || true
            sleep 2
        else
            echo -e "${BLUE}💡 使用现有VNC会话${NC}"
            return 0
        fi
    fi
    
    # 检查是否已设置VNC密码
    if [ ! -f ~/.vnc/passwd ]; then
        echo -e "${YELLOW}🔐 首次运行，请设置VNC密码:${NC}"
        vncpasswd
    fi
    
    # 启动VNC服务器
    echo "启动VNC服务器..."
    vncserver :1 -geometry 1920x1080 -depth 24 -localhost no
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ VNC服务器启动成功${NC}"
    else
        echo -e "${RED}❌ VNC服务器启动失败${NC}"
        exit 1
    fi
}

# 显示连接信息
show_connection_info() {
    echo -e "${GREEN}🎉 VNC环境启动完成!${NC}"
    echo "=================================="
    echo -e "${BLUE}📡 连接信息:${NC}"
    echo "   服务器地址: ${SERVER_IP}:5901"
    echo "   VNC端口: 5901"
    echo "   显示号: :1"
    echo ""
    echo -e "${BLUE}🖥️  VNC客户端推荐:${NC}"
    echo "   Windows: TightVNC Viewer, RealVNC Viewer"
    echo "   Mac: 内置VNC客户端 (vnc://${SERVER_IP}:5901)"
    echo "   Linux: xtightvncviewer ${SERVER_IP}:5901"
    echo ""
    echo -e "${BLUE}🔒 安全连接 (推荐):${NC}"
    echo "   ssh -L 5901:localhost:5901 $(whoami)@${SERVER_IP}"
    echo "   然后连接到: localhost:5901"
    echo ""
    echo -e "${BLUE}🧪 测试Open3D:${NC}"
    echo "   在VNC桌面中打开终端，运行:"
    echo "   python3 vnc_open3d_test.py"
    echo "   python3 open3d_minimal_demo.py"
}

# 主函数
main() {
    echo -e "${BLUE}开始检查和配置环境...${NC}"
    
    # 检查VNC安装
    if ! check_vnc_installed; then
        read -p "是否要安装VNC? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_vnc
            configure_vnc
        else
            echo -e "${RED}❌ 需要VNC才能继续${NC}"
            exit 1
        fi
    fi
    
    # 配置VNC (如果配置文件不存在)
    if [ ! -f ~/.vnc/xstartup ]; then
        configure_vnc
    fi
    
    # 检查Python环境
    check_python_env
    
    # 启动VNC
    start_vnc
    
    # 显示连接信息
    show_connection_info
    
    echo ""
    echo -e "${GREEN}🚀 准备就绪! 现在可以通过VNC客户端连接并使用Open3D了${NC}"
}

# 处理Ctrl+C
trap 'echo -e "\n${YELLOW}⚠️  脚本被中断${NC}"; exit 1' INT

# 运行主函数
main "$@"
