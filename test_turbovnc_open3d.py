#!/usr/bin/env python3
"""
TurboVNC环境下的Open3D测试脚本
专门为SSH隧道连接设计
"""

import numpy as np
import os
import sys

def check_environment():
    """检查TurboVNC环境"""
    print("🔍 检查TurboVNC环境...")
    
    # 检查DISPLAY
    display = os.environ.get('DISPLAY')
    print(f"DISPLAY: {display}")
    
    if not display:
        print("❌ DISPLAY环境变量未设置")
        print("💡 请确保在TurboVNC桌面环境中运行")
        return False
    
    # 检查是否在VNC会话中
    if ':1' in display or ':2' in display:
        print("✅ 检测到VNC环境")
    else:
        print("⚠️  可能不在VNC环境中")
    
    return True

def test_open3d_basic():
    """测试Open3D基础功能"""
    print("\n📦 测试Open3D基础功能...")
    
    try:
        import open3d as o3d
        print(f"✅ Open3D版本: {o3d.__version__}")
        
        # 创建简单几何体
        mesh = o3d.geometry.TriangleMesh.create_sphere(radius=1.0)
        mesh.paint_uniform_color([1, 0, 0])
        
        print("✅ 几何体创建成功")
        return True
        
    except ImportError:
        print("❌ Open3D未安装")
        print("💡 安装命令: pip3 install open3d")
        return False
    except Exception as e:
        print(f"❌ Open3D测试失败: {e}")
        return False

def test_visualization():
    """测试可视化功能"""
    print("\n🎨 测试TurboVNC可视化...")
    
    try:
        import open3d as o3d
        
        # 创建测试场景
        print("创建测试场景...")
        
        # 球体
        sphere = o3d.geometry.TriangleMesh.create_sphere(radius=0.5)
        sphere.translate([0, 0, 0])
        sphere.paint_uniform_color([1, 0, 0])  # 红色
        
        # 立方体
        box = o3d.geometry.TriangleMesh.create_box(width=1, height=1, depth=1)
        box.translate([2, 0, 0])
        box.paint_uniform_color([0, 1, 0])  # 绿色
        
        # 圆柱体
        cylinder = o3d.geometry.TriangleMesh.create_cylinder(radius=0.3, height=1.5)
        cylinder.translate([-2, 0, 0])
        cylinder.paint_uniform_color([0, 0, 1])  # 蓝色
        
        # 坐标系
        coord = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
        
        geometries = [sphere, box, cylinder, coord]
        
        print("✅ 测试场景创建完成")
        print("\n🖼️  启动TurboVNC可视化窗口...")
        print("💡 操作提示:")
        print("   - 鼠标左键拖拽: 旋转视角")
        print("   - 鼠标右键拖拽: 平移")
        print("   - 滚轮: 缩放")
        print("   - Q键: 退出")
        
        # 可视化
        o3d.visualization.draw_geometries(
            geometries,
            window_name="TurboVNC + Open3D 测试",
            width=1200,
            height=800
        )
        
        print("✅ TurboVNC可视化测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        print("\n🔧 可能的解决方案:")
        print("   1. 确保TurboVNC服务器正在运行")
        print("   2. 检查桌面环境是否正常")
        print("   3. 重启VNC会话")
        return False

def test_pointcloud_demo():
    """测试点云演示"""
    print("\n☁️  测试点云可视化...")
    
    try:
        import open3d as o3d
        
        # 创建随机点云
        print("生成随机点云...")
        n_points = 1000
        points = np.random.randn(n_points, 3)
        colors = np.random.rand(n_points, 3)
        
        # 创建点云对象
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        pcd.colors = o3d.utility.Vector3dVector(colors)
        
        # 创建边界框
        bbox = o3d.geometry.OrientedBoundingBox(
            center=[0, 0, 0],
            R=np.eye(3),
            extent=[3, 3, 3]
        )
        bbox.color = [1, 0, 0]
        
        # 创建坐标系
        coord = o3d.geometry.TriangleMesh.create_coordinate_frame(size=2.0)
        
        print("✅ 点云数据创建完成")
        print("\n🖼️  启动点云可视化...")
        
        # 可视化点云
        o3d.visualization.draw_geometries(
            [pcd, bbox, coord],
            window_name="TurboVNC 点云演示",
            width=1400,
            height=900,
            point_show_normal=False
        )
        
        print("✅ 点云可视化测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 点云测试失败: {e}")
        return False

def show_connection_info():
    """显示连接信息"""
    print("\n" + "="*60)
    print("📡 TurboVNC连接信息")
    print("="*60)
    print("🔗 SSH隧道命令:")
    print("   ssh -L 5901:localhost:5901 root@************ -p 1020")
    print("")
    print("🖥️  VNC客户端连接:")
    print("   地址: localhost:5901")
    print("   端口: 5901")
    print("")
    print("📱 推荐VNC客户端:")
    print("   • TurboVNC Viewer (最佳)")
    print("   • TightVNC Viewer")
    print("   • RealVNC Viewer")
    print("")
    print("🎮 TurboVNC管理:")
    print("   启动: /opt/TurboVNC/bin/vncserver :1")
    print("   停止: /opt/TurboVNC/bin/vncserver -kill :1")
    print("   查看: /opt/TurboVNC/bin/vncserver -list")

def main():
    """主测试函数"""
    print("🚀 TurboVNC + Open3D 测试程序")
    print("专为SSH隧道连接设计")
    print("="*50)
    
    # 显示连接信息
    show_connection_info()
    
    # 测试序列
    tests = [
        ("环境检查", check_environment),
        ("Open3D基础测试", test_open3d_basic),
        ("基础可视化测试", test_visualization),
        ("点云可视化测试", test_pointcloud_demo)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n{'='*15} {name} {'='*15}")
        
        if test_func():
            passed += 1
            print(f"✅ {name} 通过")
        else:
            print(f"❌ {name} 失败")
            
            # 如果基础测试失败，询问是否继续
            if name in ["环境检查", "Open3D基础测试"]:
                response = input("\n⚠️  基础测试失败，是否继续? (y/n): ")
                if response.lower() != 'y':
                    break
    
    print("\n" + "="*50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        print("✨ TurboVNC环境下Open3D运行完美!")
        print("\n💡 现在你可以:")
        print("   1. 运行你的Open3D项目")
        print("   2. 使用完整的3D可视化功能")
        print("   3. 享受流畅的远程3D体验")
    elif passed >= 2:
        print("✅ 基本功能正常!")
        print("💡 可以开始使用Open3D进行开发")
    else:
        print("⚠️  环境配置需要调整")
        print("\n🔧 建议检查:")
        print("   1. TurboVNC服务器状态")
        print("   2. SSH隧道连接")
        print("   3. VNC客户端设置")
        print("   4. Open3D安装")

if __name__ == "__main__":
    main()
